/**
 * 用户引导系统样式
 * 
 * 集成项目主题系统，支持响应式设计和可访问性
 * 基于Driver.js的自定义样式覆盖
 */

/* ===========================
   Driver.js 基础样式覆盖
   ========================== */

.user-guide-driver {
    /* 使用项目CSS变量 */
    --guide-primary-color: var(--primary-color, #2196f3);
    --guide-primary-hover: var(--primary-hover, #1976d2);
    --guide-background: var(--surface-color, #ffffff);
    --guide-text-color: var(--text-color, #333333);
    --guide-text-light: var(--text-light, #666666);
    --guide-border-color: var(--border-color, #e0e0e0);
    --guide-shadow: var(--shadow-color, rgba(0, 0, 0, 0.1));
    --guide-radius: var(--radius-lg, 8px);
    --guide-font-family: var(--font-family, "PingFang SC", "Helvetica Neue", Arial, sans-serif);
}

/* ===========================
   遮罩层样式优化
   ========================== */

/* 统一的半透明深色遮罩 - 替代全黑遮罩 */
.driver-overlay,
.user-guide-driver .driver-overlay,
.user-guide-driver-enhanced .driver-overlay {
    background-color: rgba(0, 0, 0, 0.75) !important; /* 半透明深色遮罩 */
    backdrop-filter: blur(2px) !important; /* 添加轻微模糊效果 */
    transition: all 0.3s ease-in-out !important;
    z-index: 999999 !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    pointer-events: auto !important;
}

/* 高对比度主题的遮罩 */
.user-guide-driver.high-contrast .driver-overlay,
.user-guide-driver-enhanced.high-contrast .driver-overlay,
@media (prefers-contrast: high) {
    .driver-overlay {
        background-color: rgba(0, 0, 0, 0.85) !important; /* 更深的遮罩以提高对比度 */
    }
}

/* 高亮区域样式 */
.driver-highlighted-element {
    box-shadow: 0 0 0 4px var(--guide-primary-color), 
                0 0 0 8px rgba(33, 150, 243, 0.3) !important;
    border-radius: var(--guide-radius) !important;
    transition: all 0.3s ease-in-out !important;
}

/* 弹出框容器 */
.driver-popover {
    background: var(--guide-background) !important;
    border: 2px solid var(--guide-border-color) !important; /* 增加边框宽度 */
    border-radius: var(--guide-radius) !important;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.25),
                0 4px 16px rgba(0, 0, 0, 0.15) !important; /* 增强阴影 */
    font-family: var(--guide-font-family) !important;
    max-width: 360px !important; /* 稍微增加宽度 */
    min-width: 300px !important; /* 增加最小宽度 */
    z-index: 1000000 !important; /* 确保在最高层级 */
    backdrop-filter: blur(10px) !important; /* 添加背景模糊 */
}

/* 弹出框箭头 */
.driver-popover.driver-popover-top .driver-popover-arrow,
.driver-popover.driver-popover-bottom .driver-popover-arrow,
.driver-popover.driver-popover-left .driver-popover-arrow,
.driver-popover.driver-popover-right .driver-popover-arrow {
    border-color: var(--guide-background) !important;
}

/* 弹出框标题 */
.driver-popover-title {
    color: var(--guide-text-color) !important;
    font-size: 20px !important; /* 增大标题字体 */
    font-weight: 700 !important; /* 增加字重 */
    margin: 0 0 12px 0 !important;
    padding: 16px 16px 0 16px !important;
    line-height: 1.3 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important; /* 添加文字阴影 */
}

/* 弹出框描述 */
.driver-popover-description {
    color: var(--guide-text-color) !important; /* 使用主文字颜色而非浅色 */
    font-size: 15px !important; /* 增大描述文字 */
    font-weight: 500 !important; /* 增加字重 */
    line-height: 1.6 !important;
    margin: 0 !important;
    padding: 0 16px 16px 16px !important;
}

/* 强调文字样式 */
.driver-popover-description strong {
    color: var(--guide-primary-color) !important; /* 强调文字使用主色 */
    font-weight: 700 !important;
}

/* 弹出框底部 */
.driver-popover-footer {
    padding: 16px 20px 20px 20px !important; /* 增加内边距 */
    border-top: 1px solid var(--guide-border-color) !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    gap: 12px !important; /* 增加按钮间距 */
}

/* 进度指示器样式 */
.driver-popover-progress-text {
    color: var(--guide-primary-color) !important; /* 使用主色显示进度 */
    font-size: 13px !important;
    font-weight: 600 !important;
    margin-bottom: 12px !important;
    text-align: center !important;
    background: rgba(33, 150, 243, 0.1) !important; /* 浅蓝背景 */
    padding: 6px 12px !important;
    border-radius: 6px !important;
    border: 1px solid rgba(33, 150, 243, 0.2) !important;
}

/* 按钮基础样式 */
.driver-popover-footer button {
    font-family: var(--guide-font-family) !important;
    font-size: 15px !important; /* 增大按钮文字 */
    font-weight: 600 !important; /* 增加字重 */
    padding: 10px 20px !important; /* 增大按钮 */
    border-radius: 8px !important; /* 增大圆角 */
    border: none !important;
    cursor: pointer !important;
    transition: all 0.2s ease-in-out !important;
    min-width: 80px !important; /* 增加最小宽度 */
}

/* 主要按钮（下一步、完成） */
.driver-popover-next-btn,
.driver-popover-done-btn {
    background: var(--guide-primary-color) !important;
    color: white !important;
    font-weight: 700 !important; /* 主按钮字重更大 */
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3) !important; /* 添加默认阴影 */
}

.driver-popover-next-btn:hover,
.driver-popover-done-btn:hover {
    background: var(--guide-primary-hover) !important;
    transform: translateY(-2px) !important; /* 增加悬停位移 */
    box-shadow: 0 6px 16px rgba(33, 150, 243, 0.4) !important; /* 增强悬停阴影 */
}

/* 次要按钮（上一步） */
.driver-popover-prev-btn {
    background: #f8f9fa !important; /* 浅灰背景 */
    color: var(--guide-text-color) !important; /* 使用主文字颜色 */
    border: 2px solid var(--guide-border-color) !important; /* 增加边框宽度 */
}

.driver-popover-prev-btn:hover {
    background: #e9ecef !important;
    border-color: var(--guide-primary-color) !important;
    color: var(--guide-primary-color) !important;
    transform: translateY(-1px) !important; /* 添加悬停效果 */
}

/* 关闭按钮 */
.driver-popover-close-btn {
    background: transparent !important;
    color: #6c757d !important; /* 使用固定的中性色 */
    border: 1px solid #dee2e6 !important; /* 使用固定的边框色 */
}

.driver-popover-close-btn:hover {
    color: #dc3545 !important; /* 红色悬停 */
    border-color: #dc3545 !important;
    background: rgba(220, 53, 69, 0.1) !important; /* 浅红背景 */
    transform: translateY(-1px) !important; /* 添加悬停效果 */
}

/* ===========================
   自定义步骤样式
   ========================== */

/* 欢迎步骤 */
.guide-step-welcome .driver-popover {
    max-width: 400px !important;
    text-align: center !important;
}

.guide-step-welcome .driver-popover-title {
    font-size: 24px !important;
    background: linear-gradient(135deg, var(--guide-primary-color), #9c27b0) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

/* 完成步骤 */
.guide-step-complete .driver-popover {
    max-width: 380px !important;
    text-align: center !important;
}

.guide-step-complete .driver-popover-title {
    font-size: 20px !important;
    color: var(--success-color, #4caf50) !important;
}

/* AI助手步骤特殊样式 */
.guide-step-ai .driver-popover-title {
    color: var(--guide-primary-color) !important;
}

/* ===========================
   响应式设计
   ========================== */

/* 平板设备 */
@media (max-width: 768px) {
    .driver-popover {
        max-width: 280px !important;
        min-width: 240px !important;
    }
    
    .driver-popover-title {
        font-size: 16px !important;
        padding: 12px 12px 0 12px !important;
    }
    
    .driver-popover-description {
        font-size: 13px !important;
        padding: 0 12px 12px 12px !important;
    }
    
    .driver-popover-footer {
        padding: 8px 12px 12px 12px !important;
        flex-direction: column !important;
        gap: 6px !important;
    }
    
    .driver-popover-footer button {
        width: 100% !important;
        font-size: 13px !important;
        padding: 10px 16px !important;
    }
}

/* 手机设备 */
@media (max-width: 480px) {
    .driver-popover {
        max-width: 260px !important;
        min-width: 220px !important;
    }
    
    .guide-step-welcome .driver-popover,
    .guide-step-complete .driver-popover {
        max-width: 300px !important;
    }
    
    .driver-popover-title {
        font-size: 15px !important;
    }
    
    .driver-popover-description {
        font-size: 12px !important;
    }
    
    /* 确保在小屏幕上弹出框不会超出视口 */
    .driver-popover {
        margin: 10px !important;
    }
}

/* ===========================
   可访问性增强
   ========================== */

/* 焦点样式 */
.driver-popover-footer button:focus {
    outline: 2px solid var(--guide-primary-color) !important;
    outline-offset: 2px !important;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .driver-overlay {
        background: rgba(0, 0, 0, 0.9) !important;
    }
    
    .driver-highlighted-element {
        box-shadow: 0 0 0 4px #ffffff, 
                    0 0 0 8px #000000 !important;
    }
    
    .driver-popover {
        border: 2px solid var(--guide-text-color) !important;
    }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
    .driver-overlay,
    .driver-highlighted-element,
    .driver-popover-footer button {
        transition: none !important;
    }
}

/* ===========================
   主题适配
   ========================== */

/* 深色主题适配 */
[data-theme="dark"] .user-guide-driver,
.theme-dark .user-guide-driver {
    --guide-background: var(--surface-color, #2d2d2d);
    --guide-text-color: var(--text-color, #ffffff);
    --guide-text-light: var(--text-light, #cccccc);
    --guide-border-color: var(--border-color, #404040);
    --guide-shadow: rgba(0, 0, 0, 0.3);
}

/* 外星人主题适配 */
[data-theme="alien"] .user-guide-driver,
.theme-alien .user-guide-driver {
    --guide-primary-color: var(--alien-primary, #00ff88);
    --guide-primary-hover: var(--alien-primary-hover, #00cc6a);
}

/* ===========================
   动画效果
   ========================== */

/* 弹出框出现动画 */
@keyframes guidePopoverFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.driver-popover {
    animation: guidePopoverFadeIn 0.3s ease-out !important;
}

/* 高亮元素脉冲效果 */
@keyframes guidePulse {
    0%, 100% {
        box-shadow: 0 0 0 4px var(--guide-primary-color), 
                    0 0 0 8px rgba(33, 150, 243, 0.3);
    }
    50% {
        box-shadow: 0 0 0 4px var(--guide-primary-color), 
                    0 0 0 12px rgba(33, 150, 243, 0.2);
    }
}

.driver-highlighted-element {
    animation: guidePulse 2s ease-in-out infinite !important;
}

/* ===========================
   工具类
   ========================== */

/* 隐藏引导元素 */
.user-guide-hidden {
    display: none !important;
}

/* 引导进行中的body样式 */
.user-guide-active {
    overflow: hidden !important;
}

/* 引导步骤计数器 */
.guide-step-counter {
    position: absolute;
    top: -12px;
    right: -12px;
    background: var(--guide-primary-color);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* ===========================
   完成消息动画
   ========================== */

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* 引导完成消息样式 */
.guide-completion-message {
    font-family: var(--guide-font-family);
}

.guide-completion-message .message-content {
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--guide-text-color);
    font-size: 14px;
    line-height: 1.4;
}

.guide-completion-message .message-content i {
    font-size: 18px;
    flex-shrink: 0;
}

/* ===========================
   调试和开发工具
   ========================== */

/* 调试模式样式 */
.user-guide-debug {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px;
    border-radius: 6px;
    font-family: monospace;
    font-size: 12px;
    z-index: 10002;
    max-width: 300px;
}

.user-guide-debug h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #4CAF50;
}

.user-guide-debug ul {
    margin: 0;
    padding-left: 16px;
    list-style: none;
}

.user-guide-debug li {
    margin: 4px 0;
    padding-left: 16px;
    position: relative;
}

.user-guide-debug li:before {
    content: "•";
    position: absolute;
    left: 0;
    color: #2196F3;
}

/* 重置按钮样式 */
.guide-reset-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: var(--error-color, #f44336);
    color: white;
    border: none;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    font-size: 18px;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
    z-index: 10002;
    transition: all 0.2s ease-in-out;
}

.guide-reset-btn:hover {
    background: var(--error-dark, #d32f2f);
    transform: scale(1.1);
}

.guide-reset-btn:active {
    transform: scale(0.95);
}
