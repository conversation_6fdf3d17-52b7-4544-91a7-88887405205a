<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户引导系统测试</title>
    <link rel="stylesheet" href="user-guide-enhanced.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            background: #1976d2;
            transform: translateY(-1px);
        }
        .sidebar {
            width: 200px;
            height: 300px;
            background: #34495e;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .user-profile-dropdown {
            position: relative;
            display: inline-block;
            margin: 20px;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
        }
        .user-dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            min-width: 200px;
            z-index: 1000;
            display: none;
        }
        .user-dropdown-menu.show {
            display: block;
        }
        .dropdown-item {
            padding: 12px 16px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .dropdown-item:hover {
            background: #f5f5f5;
        }
        .dropdown-item:last-child {
            border-bottom: none;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 用户引导系统测试</h1>
        
        <div class="status">
            <strong>测试状态：</strong>
            <span id="test-status">准备就绪</span>
        </div>

        <h2>测试按钮</h2>
        <button class="test-button" onclick="testBasicGuide()">测试基础引导</button>
        <button class="test-button" onclick="testEnhancedGuide()">测试增强引导</button>
        <button class="test-button" onclick="testUserMenuGuide()">测试用户菜单引导</button>

        <h2>测试元素</h2>
        <div class="sidebar">
            <h3>侧边栏</h3>
            <p>这是一个测试侧边栏，用于验证引导系统的高亮功能。</p>
        </div>

        <div class="user-profile-dropdown">
            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&q=80" 
                 alt="用户头像" class="user-avatar" id="test-user-avatar">
            <div class="user-dropdown-menu" id="test-user-dropdown-menu">
                <div class="dropdown-item" id="test-header-user-guide-item">
                    <span>🎯</span>
                    <span>新手引导</span>
                </div>
                <div class="dropdown-item">
                    <span>⚙️</span>
                    <span>账号设置</span>
                </div>
                <div class="dropdown-item">
                    <span>🚪</span>
                    <span>退出登录</span>
                </div>
            </div>
        </div>

        <h2>测试日志</h2>
        <div id="test-log" style="background: #f8f9fa; padding: 15px; border-radius: 8px; font-family: monospace; max-height: 300px; overflow-y: auto;"></div>
    </div>

    <!-- 加载Driver.js -->
    <script src="https://cdn.jsdelivr.net/npm/driver.js@1.3.1/dist/driver.js.iife.js"></script>
    
    <script>
        // 测试日志函数
        function log(message) {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function updateStatus(status) {
            document.getElementById('test-status').textContent = status;
            log(`状态更新: ${status}`);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成');
            
            // 检查Driver.js是否加载
            if (window.driver) {
                log('✅ Driver.js 已加载');
                log(`Driver.js 类型: ${typeof window.driver}`);
                log(`Driver.js 内容: ${Object.keys(window.driver || {}).join(', ')}`);
                updateStatus('Driver.js 已就绪');
            } else {
                log('❌ Driver.js 未加载');
                updateStatus('Driver.js 加载失败');
            }

            // 设置用户头像点击事件
            const userAvatar = document.getElementById('test-user-avatar');
            const userDropdown = document.getElementById('test-user-dropdown-menu');
            
            userAvatar.addEventListener('click', function(e) {
                e.stopPropagation();
                log('用户头像被点击');
                userDropdown.classList.toggle('show');
            });

            // 设置新手引导按钮点击事件
            const guideBtn = document.getElementById('test-header-user-guide-item');
            guideBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                log('新手引导按钮被点击');
                userDropdown.classList.remove('show');
                setTimeout(() => {
                    testEnhancedGuide();
                }, 200);
            });

            // 点击其他地方关闭菜单
            document.addEventListener('click', function() {
                userDropdown.classList.remove('show');
            });
        });

        // 测试基础引导
        function testBasicGuide() {
            log('开始测试基础引导...');
            updateStatus('运行基础引导测试');
            
            if (!window.driver) {
                log('❌ Driver.js 未加载，无法测试');
                updateStatus('测试失败 - Driver.js 未加载');
                return;
            }

            try {
                const DriverConstructor = window.driver?.driver || window.Driver;
                const driverInstance = DriverConstructor({
                    showProgress: true,
                    steps: [
                        {
                            element: 'body',
                            popover: {
                                title: '👋 基础引导测试',
                                description: '这是一个基础的引导测试，验证Driver.js是否正常工作。',
                                position: 'center'
                            }
                        },
                        {
                            element: '.sidebar',
                            popover: {
                                title: '📋 侧边栏测试',
                                description: '这是侧边栏的引导测试。',
                                position: 'right'
                            }
                        }
                    ]
                });

                driverInstance.drive();
                log('✅ 基础引导启动成功');
                updateStatus('基础引导运行中');
            } catch (error) {
                log(`❌ 基础引导启动失败: ${error.message}`);
                updateStatus('基础引导测试失败');
            }
        }

        // 测试增强引导
        function testEnhancedGuide() {
            log('开始测试增强引导...');
            updateStatus('运行增强引导测试');
            
            if (!window.driver) {
                log('❌ Driver.js 未加载，无法测试');
                updateStatus('测试失败 - Driver.js 未加载');
                return;
            }

            try {
                // 尝试不同的Driver.js调用方式
                let DriverConstructor;
                if (window.driver && window.driver.js && typeof window.driver.js === 'function') {
                    DriverConstructor = window.driver.js;
                } else if (window.driver && typeof window.driver === 'function') {
                    DriverConstructor = window.driver;
                } else if (window.driver && window.driver.driver) {
                    DriverConstructor = window.driver.driver;
                } else if (window.Driver) {
                    DriverConstructor = window.Driver;
                } else {
                    throw new Error('无法找到Driver.js构造函数');
                }

                log(`使用构造函数: ${DriverConstructor.name || 'anonymous'}`);
                const driverInstance = DriverConstructor({
                    className: 'user-guide-driver-enhanced',
                    animate: true,
                    opacity: 0.7,
                    padding: 20,
                    showProgress: true,
                    doneBtnText: '完成引导',
                    closeBtnText: '跳过',
                    nextBtnText: '下一步',
                    prevBtnText: '上一步',
                    steps: [
                        {
                            element: 'body',
                            popover: {
                                title: '🎉 增强引导测试',
                                description: '这是增强版的引导测试，使用了优化的样式和配置。注意遮罩的透明度和文字的可读性。',
                                position: 'center'
                            }
                        },
                        {
                            element: '.sidebar',
                            popover: {
                                title: '📋 侧边栏高亮',
                                description: '侧边栏现在被高亮显示，注意边框效果和阴影。',
                                position: 'right'
                            }
                        },
                        {
                            element: '.user-profile-dropdown',
                            popover: {
                                title: '👤 用户菜单',
                                description: '这是用户头像和下拉菜单区域。点击头像可以打开菜单。',
                                position: 'bottom'
                            }
                        }
                    ]
                });

                driverInstance.drive();
                log('✅ 增强引导启动成功');
                updateStatus('增强引导运行中');
            } catch (error) {
                log(`❌ 增强引导启动失败: ${error.message}`);
                updateStatus('增强引导测试失败');
            }
        }

        // 测试用户菜单引导
        function testUserMenuGuide() {
            log('开始测试用户菜单引导...');
            updateStatus('运行用户菜单引导测试');
            
            // 先打开用户菜单
            const userDropdown = document.getElementById('test-user-dropdown-menu');
            userDropdown.classList.add('show');
            log('用户菜单已打开');

            setTimeout(() => {
                if (!window.driver) {
                    log('❌ Driver.js 未加载，无法测试');
                    updateStatus('测试失败 - Driver.js 未加载');
                    return;
                }

                try {
                    const DriverConstructor = window.driver?.driver || window.Driver;
                    const driverInstance = DriverConstructor({
                        className: 'user-guide-driver-enhanced',
                        animate: true,
                        opacity: 0.7,
                        padding: 20,
                        showProgress: true,
                        steps: [
                            {
                                element: '#test-header-user-guide-item',
                                popover: {
                                    title: '🎯 新手引导按钮',
                                    description: '点击这个按钮可以启动新手引导。这是测试用户菜单中引导按钮的功能。',
                                    position: 'left'
                                }
                            }
                        ]
                    });

                    driverInstance.drive();
                    log('✅ 用户菜单引导启动成功');
                    updateStatus('用户菜单引导运行中');
                } catch (error) {
                    log(`❌ 用户菜单引导启动失败: ${error.message}`);
                    updateStatus('用户菜单引导测试失败');
                }
            }, 300);
        }
    </script>
</body>
</html>
